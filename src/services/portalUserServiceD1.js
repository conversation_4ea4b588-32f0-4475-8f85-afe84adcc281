// src/services/portalUserServiceD1.js
// D1-based PortalUserService to replace KV storage operations

import { TokenService } from "./tokenService.js";
import { DatabaseService } from "./databaseService.js";

export class PortalUserService {
  constructor(env) {
    this.env = env;
    this.db = new DatabaseService(env);
    this.tokenService = new TokenService(env);
  }

  async createPortalUser(userData) {
    console.log("Creating portal user:", userData);

    try {
      const userId = `portal_${Date.now()}_${Math.random()
        .toString(36)
        .slice(2, 11)}`;
      
      // Only generate activation token for non-Google signups
      const activationToken = userData.googleId
        ? null
        : `act_${Date.now()}_${Math.random().toString(36).slice(2, 17)}`;

      console.log("Generated activationToken:", activationToken);

      const now = new Date().toISOString();

      // Prepare profile data as separate fields
      const username = userData.email.split("@")[0];
      const displayName = userData.displayName || userData.email.split("@")[0];

      const user = {
        id: userId,
        email: userData.email,
        password: userData.password, // This will be a random UUID for Google users
        type: "portal",
        status: "active",
        created_at: now,
        updated_at: now,
        last_login: null,
        is_active: userData.googleId ? 1 : 0, // Activate Google users immediately
        activated_at: userData.googleId ? now : null,
        google_id: userData.googleId || null,
        picture: userData.picture || null,
        username: username,
        display_name: displayName,
      };

      console.log("User object before saving:", JSON.stringify(user, null, 2));

      // Create user in database
      await this.db.createPortalUser(user);

      // Store activation token if needed (only for non-Google users)
      if (activationToken) {
        await this.db.createActivationToken({
          token: activationToken,
          user_id: userId,
          created_at: now,
          expires_at: null // No expiration for now
        });
      }

      console.log("✅ Portal user created successfully:", userId);

      // Return user object with profile data for consistency
      const returnUser = {
        ...user,
        activationToken: activationToken,
        profile: {
          username: username,
          displayName: displayName
        },
        createdAt: now, // For backward compatibility
        isActive: user.is_active
      };

      console.log("Returning user object:", JSON.stringify(returnUser, null, 2));

      // Validate the user object before returning (only check for non-Google users)
      if (!userData.googleId && !activationToken) {
        console.error("❌ CRITICAL: activationToken missing from user object!");
        throw new Error("Activation token generation failed");
      }

      return returnUser;
    } catch (error) {
      console.error("❌ Error creating portal user:", error);
      throw error;
    }
  }

  async getPortalUserByEmail(email) {
    try {
      const user = await this.db.getPortalUserByEmail(email);
      if (!user) {
        return null;
      }

      // Parse profile data and convert to expected format
      return this._formatUserForResponse(user);
    } catch (error) {
      console.error("❌ Error fetching portal user by email:", error);
      return null;
    }
  }

  async getPortalUserById(userId) {
    try {
      const user = await this.db.getPortalUserById(userId);
      if (!user) {
        return null;
      }

      // Parse profile data and convert to expected format
      return this._formatUserForResponse(user);
    } catch (error) {
      console.error("❌ Error fetching portal user by ID:", error);
      return null;
    }
  }

  async updatePortalUser(userId, updateData) {
    try {
      const existingUser = await this.getPortalUserById(userId);
      if (!existingUser) {
        throw new Error("Portal user not found");
      }

      // Prepare update data
      const dbUpdateData = {
        ...updateData,
        updated_at: new Date().toISOString(),
      };

      // Handle profile data if provided
      if (updateData.profile) {
        dbUpdateData.username = updateData.profile.username;
        dbUpdateData.display_name = updateData.profile.displayName;
        delete dbUpdateData.profile;
      }

      await this.db.updatePortalUser(userId, dbUpdateData);
      console.log("✅ Portal user updated successfully:", userId);
      
      // Return updated user
      return await this.getPortalUserById(userId);
    } catch (error) {
      console.error("❌ Error updating portal user:", error);
      throw error;
    }
  }

  async updateLastLogin(userId) {
    try {
      await this.db.updatePortalUser(userId, {
        last_login: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error("❌ Error updating last login:", error);
    }
  }

  async validatePortalCredentials(email, password) {
    try {
      const user = await this.getPortalUserByEmail(email);
      if (!user) {
        return { valid: false, message: "User not found" };
      }

      if (user.isActive === 0) {
        return {
          valid: false,
          message:
            "Account not activated. Please check your email and click the activation link.",
        };
      }

      // In production, use proper password hashing comparison
      if (user.password !== password) {
        return { valid: false, message: "Invalid credentials" };
      }

      return { valid: true, user };
    } catch (error) {
      console.error("❌ Error validating portal credentials:", error);
      return { valid: false, message: "Authentication error" };
    }
  }

  async getAllPortalUsers() {
    try {
      const users = await this.db.getAllPortalUsers();
      
      return users.map(user => {
        const formattedUser = this._formatUserForResponse(user);
        // Don't return password in list
        delete formattedUser.password;
        return formattedUser;
      });
    } catch (error) {
      console.error("❌ Error fetching all portal users:", error);
      return [];
    }
  }

  async deletePortalUser(userId) {
    try {
      const user = await this.getPortalUserById(userId);
      if (!user) {
        throw new Error("Portal user not found");
      }

      console.log(
        `🗑️ Starting comprehensive deletion for user: ${user.email} (${userId})`
      );

      // Delete user and all related data using database service
      const results = await this.db.deletePortalUser(userId);

      console.log(
        "✅ Portal user and all associated data deleted successfully:",
        userId
      );

      return {
        success: true,
        userId,
        email: user.email,
        deletedOperations: results.length,
        message: "User and all associated data deleted successfully",
      };
    } catch (error) {
      console.error("❌ Error deleting portal user:", error);
      throw error;
    }
  }

  async deletePortalUserByEmail(email) {
    try {
      console.log(`🔍 Looking up user by email: ${email}`);

      // Get user by email
      const user = await this.getPortalUserByEmail(email);
      if (!user) {
        throw new Error(`Portal user not found with email: ${email}`);
      }

      console.log(`✅ Found user: ${user.id} (${user.email})`);

      // Call the comprehensive delete method
      return await this.deletePortalUser(user.id);
    } catch (error) {
      console.error("❌ Error deleting portal user by email:", error);
      throw error;
    }
  }

  async verifyPortalUser(email, password) {
    const user = await this.getPortalUserByEmail(email);

    if (!user) {
      return null;
    }

    // IMPORTANT: Replace with a secure password hashing and comparison function
    if (user.password !== password) {
      return null;
    }

    await this.updateLastLogin(user.id);

    return user;
  }

  async activateUser(activationToken) {
    try {
      // Get activation token from database
      const tokenData = await this.db.getActivationToken(activationToken);
      if (!tokenData) {
        return { success: false, message: "Invalid activation token" };
      }

      // Get user data
      const user = await this.getPortalUserById(tokenData.user_id);
      if (!user) {
        return { success: false, message: "User not found" };
      }

      // Check if already activated
      if (user.isActive === 1) {
        return { success: false, message: "User already activated" };
      }

      const now = new Date().toISOString();

      // Activate user
      await this.db.updatePortalUser(tokenData.user_id, {
        is_active: 1,
        activated_at: now,
        updated_at: now
      });

      // Mark activation token as used
      await this.db.markActivationTokenUsed(activationToken);

      console.log("✅ User activated successfully:", user.email);

      const updatedUser = await this.getPortalUserById(tokenData.user_id);
      return {
        success: true,
        message: "User activated successfully",
        user: updatedUser,
      };
    } catch (error) {
      console.error("❌ Error activating user:", error);
      return { success: false, message: "Error activating user" };
    }
  }

  async resetPassword(token, newPassword) {
    try {
      console.log("🔄 Starting password reset process...");
      console.log(`   Token provided: ${token ? "YES" : "NO"}`);
      console.log(`   New password provided: ${newPassword ? "YES" : "NO"}`);

      // Verify the password reset token
      const decodedToken = await this.tokenService.verifyPasswordResetToken(
        token
      );

      if (!decodedToken.success) {
        console.log("❌ Token verification failed:", decodedToken.error);
        return { success: false, message: "Invalid or expired token." };
      }

      const { userId } = decodedToken.data;
      console.log(`✅ Token verified for user ID: ${userId}`);

      // Get user data
      const user = await this.getPortalUserById(userId);

      if (!user) {
        console.log("❌ User not found for ID:", userId);
        return { success: false, message: "User not found." };
      }

      console.log(`✅ User found: ${user.email}`);

      // Check if token has already been used
      const tokenRecord = await this.db.getPasswordResetToken(token);
      if (!tokenRecord) {
        console.log("❌ Token has already been used or doesn't exist");
        return {
          success: false,
          message: "This password reset link has already been used or is invalid.",
        };
      }

      // Mark the token as used
      await this.db.markPasswordResetTokenUsed(token);
      console.log("✅ Token marked as used");

      // Update the user's password
      await this.db.updatePortalUser(userId, {
        password: newPassword, // In a real app, hash this password
        updated_at: new Date().toISOString()
      });

      console.log(`✅ Password reset successfully for user: ${user.email}`);
      return {
        success: true,
        message: "Password has been reset successfully.",
      };
    } catch (error) {
      console.error("❌ Error in resetPassword service:", error);
      return {
        success: false,
        message: "An error occurred during password reset.",
      };
    }
  }

  // Helper method to format user data for response
  _formatUserForResponse(user) {
    if (!user) return null;

    // Create profile from separate fields
    const profile = {
      username: user.username || user.email.split("@")[0],
      displayName: user.display_name || user.email.split("@")[0],
    };

    return {
      id: user.id,
      email: user.email,
      password: user.password,
      type: user.type,
      createdAt: user.created_at,
      lastLogin: user.last_login,
      isActive: user.is_active,
      activatedAt: user.activated_at,
      googleId: user.google_id,
      picture: user.picture,
      profile: profile,
      status: user.status,
      updatedAt: user.updated_at
    };
  }
}
